package dreamboard

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/shareddream"

	"github.com/dsoplabs/dinbora-backend/internal/repository/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialsheet"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateDreamResponseDTO represents the response when creating a dream
type CreateDreamResponseDTO struct {
	Dreams   []*model.Dream `json:"dreams"`
	ShareURL *string        `json:"shareUrl,omitempty"`
}

type Service interface {
	// CRUD
	Create(ctx context.Context, dreamboard *model.Dreamboard) (string, error)
	Find(ctx context.Context, id string) (*model.Dreamboard, error)
	FindAll(ctx context.Context) ([]*model.Dreamboard, error)
	FindByUser(ctx context.Context, userID string) (*model.Dreamboard, error)
	Update(ctx context.Context, dreamboard *model.Dreamboard) error
	Delete(ctx context.Context, id string) error

	// Category CRUD
	CreateCategory(ctx context.Context, dreamboard *model.Dreamboard, category *model.Category) (*model.Category, error)
	FindCategory(ctx context.Context, dreamboard *model.Dreamboard, categoryID string) (*model.Category, error)
	UpdateCategory(ctx context.Context, dreamboard *model.Dreamboard, category *model.Category) (*model.Category, error)
	DeleteCategory(ctx context.Context, dreamboard *model.Dreamboard, categoryID string) error

	// Dream CRUD
	CreateDream(ctx context.Context, dreamboard *model.Dreamboard, dream *model.Dream) (*CreateDreamResponseDTO, error)
	FindDream(ctx context.Context, dreamboard *model.Dreamboard, dreamID string) (*model.Dream, error)
	UpdateDream(ctx context.Context, dreamboard *model.Dreamboard, dream *model.Dream) ([]*model.Dream, error)
	RemoveDream(ctx context.Context, dreamboard *model.Dreamboard, dream *model.Dream) ([]*model.Dream, error)

	// Shared Dreams
	FindPersonalDreams(ctx context.Context, userID string) ([]*model.Dream, error)
	FindSharedDreams(ctx context.Context, userID string) ([]*model.Dream, error)

	// Utility
	Initialize(ctx context.Context, userID string) error
}

type service struct {
	Repository               dreamboard.Repository
	FinancialSheetRepository financialsheet.Repository
	SharedDreamService       shareddream.Service
}

func New(repository dreamboard.Repository, financialSheetRepository financialsheet.Repository, sharedDreamService shareddream.Service) Service {
	return &service{
		Repository:               repository,
		FinancialSheetRepository: financialSheetRepository,
		SharedDreamService:       sharedDreamService,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, dreamboard *model.Dreamboard) (string, error) {
	currentTime := time.Now()
	dreamboard.CreatedAt = currentTime
	dreamboard.UpdatedAt = currentTime
	dreamboard.ComputeTotals() // Initialize the computed fields

	dreamboardID, err := s.Repository.Create(ctx, dreamboard)
	if err != nil {
		return "", err
	}

	return dreamboardID, err
}

func (s *service) Find(ctx context.Context, id string) (*model.Dreamboard, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dreamboard id", errors.BadRequest, err)
	}

	dreamboard, err := s.Repository.Find(ctx, objID)
	if err != nil {
		return nil, err
	}

	// Get SavedAmount from Financial Sheet Balance
	financialsheet, err := s.FinancialSheetRepository.FindByUser(ctx, dreamboard.User)
	if err != nil {
		return nil, err
	}

	if dreamboard != nil && !dreamboard.ObjectID.IsZero() {
		// Update computed fields before returning
		dreamboard.ID = dreamboard.ObjectID.Hex()
		dreamboard.CalculateSavedAmount(financialsheet)
		dreamboard.ComputeTotals()
	}

	for _, dream := range dreamboard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	return dreamboard, nil
}

func (s *service) FindAll(ctx context.Context) ([]*model.Dreamboard, error) {
	dreamboards, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, dreamboard := range dreamboards {
		// Get SavedAmount from Financial Sheet Balance
		financialsheet, err := s.FinancialSheetRepository.FindByUser(ctx, dreamboard.User)
		if err != nil {
			return nil, err
		}

		if dreamboard != nil && !dreamboard.ObjectID.IsZero() {
			// Update computed fields before returning
			dreamboard.ID = dreamboard.ObjectID.Hex()
			dreamboard.CalculateSavedAmount(financialsheet)
			dreamboard.ComputeTotals()
		}

		for _, dream := range dreamboard.Dreams {
			if dream != nil && !dream.ObjectID.IsZero() {
				dream.ID = dream.ObjectID.Hex()
			}
		}
	}

	return dreamboards, nil
}

func (s *service) FindByUser(ctx context.Context, userID string) (*model.Dreamboard, error) {
	dreamboard, err := s.Repository.FindByUser(ctx, userID)
	if err != nil {
		if err.(*errors.DomainError).Kind() != errors.NotFound {
			return nil, err
		}
		// Initialize new dreamboard for user
		if err := s.Initialize(ctx, userID); err != nil {
			return nil, err
		}

		// Fetch the newly created dreamboard
		dreamboard, err = s.Repository.FindByUser(ctx, userID)
		if err != nil {
			return nil, err
		}
	}

	// Get SavedAmount from Financial Sheet Balance
	financialsheet, err := s.FinancialSheetRepository.FindByUser(ctx, dreamboard.User)
	if err != nil {
		return nil, err
	}

	if dreamboard != nil && !dreamboard.ObjectID.IsZero() {
		// Update computed fields before returning
		dreamboard.ID = dreamboard.ObjectID.Hex()
		dreamboard.CalculateSavedAmount(financialsheet)
		dreamboard.ComputeTotals()
	}

	for _, dream := range dreamboard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	return dreamboard, nil
}

func (s *service) Update(ctx context.Context, dreamboard *model.Dreamboard) error {
	if err := dreamboard.Validate(); err != nil {
		return err
	}

	if dreamboard.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(dreamboard.ID)
		if err != nil {
			return errors.New(errors.Service, "invalid dreamboard ID", errors.Validation, err)
		}
		dreamboard.ObjectID = objID
	}

	dreamboard.UpdatedAt = time.Now()
	dreamboard.ComputeTotals() // Update computed fields before saving

	err := s.Repository.Update(ctx, dreamboard)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) Delete(ctx context.Context, id string) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid dreamboard ID", errors.BadRequest, err)
	}

	return s.Repository.Delete(ctx, objID)
}

// Category CRUD
func (s *service) CreateCategory(ctx context.Context, dreamboard *model.Dreamboard, category *model.Category) (*model.Category, error) {
	if err := category.Validate(); err != nil {
		return nil, err
	}

	// Check for duplicate identifier
	for _, existing := range dreamboard.Categories {
		if existing.Identifier == category.Identifier {
			return nil, errors.New(errors.Service, "category identifier already exists", errors.Conflict, nil)
		}
	}

	if err := s.Repository.CreateCategory(ctx, dreamboard.ObjectID, category); err != nil {
		return nil, err
	}

	category.ID = category.ObjectID.Hex()
	return category, nil
}

func (s *service) FindCategory(ctx context.Context, dreamboard *model.Dreamboard, categoryID string) (*model.Category, error) {
	objID, err := primitive.ObjectIDFromHex(categoryID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid category ID", errors.BadRequest, err)
	}

	category, err := s.Repository.FindCategory(ctx, dreamboard.ObjectID, objID)
	if err != nil {
		return nil, err
	}

	if category != nil && !category.ObjectID.IsZero() {
		category.ID = category.ObjectID.Hex()
	}

	return category, nil
}

func (s *service) UpdateCategory(ctx context.Context, dreamboard *model.Dreamboard, category *model.Category) (*model.Category, error) {
	if err := category.Validate(); err != nil {
		return nil, err
	}

	if category.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(category.ID)
		if err != nil {
			return nil, errors.New(errors.Service, "invalid category ID", errors.BadRequest, err)
		}
		category.ObjectID = objID
	}

	// Check for duplicate identifier (excluding the current category)
	for _, existing := range dreamboard.Categories {
		if existing.Identifier == category.Identifier && existing.ObjectID != category.ObjectID {
			return nil, errors.New(errors.Service, "category identifier already exists", errors.Conflict, nil)
		}
	}

	if err := s.Repository.UpdateCategory(ctx, dreamboard.ObjectID, category); err != nil {
		return nil, err
	}

	category.ID = category.ObjectID.Hex()
	return category, nil
}

func (s *service) DeleteCategory(ctx context.Context, dreamboard *model.Dreamboard, categoryID string) error {
	objID, err := primitive.ObjectIDFromHex(categoryID)
	if err != nil {
		return errors.New(errors.Service, "invalid category ID", errors.BadRequest, err)
	}

	// Check if category is being used by any dream
	categoryToDelete, err := s.FindCategory(ctx, dreamboard, categoryID)
	if err != nil {
		return err
	}

	for _, dream := range dreamboard.Dreams {
		if dream.Category.String() == categoryToDelete.Identifier {
			return errors.New(errors.Service, "category is in use by one or more dreams", errors.Conflict, nil)
		}
	}

	return s.Repository.DeleteCategory(ctx, dreamboard.ObjectID, objID)
}

// Dream CRUD
func (s *service) CreateDream(ctx context.Context, dreamboard *model.Dreamboard, dream *model.Dream) (*CreateDreamResponseDTO, error) {
	if err := dream.Validate(); err != nil {
		return nil, err
	}

	// Validate that the category exists in the dreamboard
	categoryExists := false
	for _, category := range dreamboard.Categories {
		if category.Identifier == dream.Category.String() {
			categoryExists = true
			break
		}
	}
	if !categoryExists {
		return nil, errors.New(errors.Service, "category does not exist in dreamboard", errors.Validation, nil)
	}

	dream.CreatedAt = time.Now()
	dream.UpdatedAt = dream.CreatedAt
	dream.Color = s.getColor(dream.Category.String())

	// Set default values for shared dream fields
	if dream.IsShared {
		dream.CreatorUserID = dreamboard.User
		dream.FundingStatus = model.FundingStatusSharedOpenForParticipants
		dream.CurrentRaisedAmount = 0

		// Calculate initial duration
		if dream.MonthlySavings > 0 {
			duration := int(dream.EstimatedCost / dream.MonthlySavings)
			dream.CalculatedDurationMonths = &duration
		}
	} else {
		dream.FundingStatus = model.FundingStatusPersonalActive
		dream.CurrentRaisedAmount = 0
	}

	if err := s.Repository.CreateDream(ctx, dreamboard.ObjectID, dream); err != nil {
		return nil, err
	}

	// Get updated dreamboard with the new dreams
	updatedBoard, err := s.Repository.Find(ctx, dreamboard.ObjectID)
	if err != nil {
		return nil, err
	}

	// Convert ObjectID to hex ID for response
	updatedBoard.ID = updatedBoard.ObjectID.Hex()

	for _, dream := range updatedBoard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Recompute totals and update the board since we added a dream
	updatedBoard.ComputeTotals()
	if err := s.Repository.Update(ctx, updatedBoard); err != nil {
		return nil, err
	}

	// Prepare response
	response := &CreateDreamResponseDTO{
		Dreams: updatedBoard.Dreams,
	}

	// If it's a shared dream, create ShareLink and creator's Contribution
	if dream.IsShared {
		if dream.ID != "" {
			// Create ShareLink
			sharedLink, err := s.SharedDreamService.CreateShareLink(ctx, dream.ID)
			if err != nil {
				return nil, err
			}

			// Generate invite URL
			shareURL := generateInviteURL(sharedLink.Token)
			response.ShareURL = &shareURL

			// Create creator's contribution
			isCreator := true // Creator is also a contributor
			_, err = s.SharedDreamService.JoinSharedDream(ctx, sharedLink.Token, dreamboard.User, isCreator, dream.MonthlySavings)
			if err != nil {
				return nil, err
			}
		}
	}

	return response, nil
}

func (s *service) FindDream(ctx context.Context, dreamboard *model.Dreamboard, dreamID string) (*model.Dream, error) {
	objID, err := primitive.ObjectIDFromHex(dreamID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dream ID", errors.BadRequest, err)
	}

	dream, err := s.Repository.FindDream(ctx, dreamboard.ObjectID, objID)
	if err != nil {
		return nil, err
	}

	if dream != nil && !dream.ObjectID.IsZero() {
		dream.ID = dream.ObjectID.Hex()
	}

	return dream, nil
}

func (s *service) UpdateDream(ctx context.Context, dreamboard *model.Dreamboard, dream *model.Dream) ([]*model.Dream, error) {
	objID, err := primitive.ObjectIDFromHex(dream.ID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dreamboard ID", errors.BadRequest, err)
	}

	// Validate dream
	if err := dream.Validate(); err != nil {
		return nil, err
	}

	// Validate that the category exists in the dreamboard
	categoryExists := false
	for _, category := range dreamboard.Categories {
		if category.Identifier == dream.Category.String() {
			categoryExists = true
			break
		}
	}
	if !categoryExists {
		return nil, errors.New(errors.Service, "category does not exist in dreamboard", errors.Validation, nil)
	}

	dream.ObjectID = objID
	dream.UpdatedAt = time.Now()

	if err := s.Repository.UpdateDream(ctx, dreamboard.ObjectID, dream); err != nil {
		return nil, err
	}

	// Get updated dreamboard
	updatedBoard, err := s.Repository.Find(ctx, dreamboard.ObjectID)
	if err != nil {
		return nil, err
	}

	// Convert ObjectID to hex ID for response
	updatedBoard.ID = updatedBoard.ObjectID.Hex()

	for _, dream := range updatedBoard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Recompute totals and update the board since we updated a dream
	updatedBoard.ComputeTotals()
	if err := s.Repository.Update(ctx, updatedBoard); err != nil {
		return nil, err
	}

	return updatedBoard.Dreams, nil
}

func (s *service) RemoveDream(ctx context.Context, dreamboard *model.Dreamboard, dream *model.Dream) ([]*model.Dream, error) {
	objID, err := primitive.ObjectIDFromHex(dream.ID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dreamboard ID", errors.BadRequest, err)
	}

	dream.ObjectID = objID
	if err := s.Repository.RemoveDream(ctx, dreamboard.ObjectID, dream.ObjectID); err != nil {
		return nil, err
	}

	// Get updated dreamboard
	updatedBoard, err := s.Repository.Find(ctx, dreamboard.ObjectID)
	if err != nil {
		return nil, err
	}

	// Convert ObjectID to hex ID for response
	updatedBoard.ID = updatedBoard.ObjectID.Hex()

	for _, dream := range updatedBoard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Recompute totals and update the board since we removed a dream
	updatedBoard.ComputeTotals()
	if err := s.Repository.Update(ctx, updatedBoard); err != nil {
		return nil, err
	}

	return updatedBoard.Dreams, nil
}

// Utility
func (s *service) Initialize(ctx context.Context, userID string) error {
	existing, err := s.Repository.FindByUser(ctx, userID)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return err
	}

	if existing != nil {
		return errors.New(errors.Service, "dreamboard already exists", errors.Conflict, nil)
	}

	currentTime := time.Now()
	newBoard := &model.Dreamboard{
		User:       userID,
		Dreams:     []*model.Dream{},
		Categories: []*model.Category{}, // Add default categories after creation
		CreatedAt:  currentTime,
		UpdatedAt:  currentTime,
	}

	newBoard.ComputeTotals() // Initialize computed fields to zero since there are no dreams

	if err := newBoard.Validate(); err != nil {
		return err
	}

	boardID, err := s.Repository.Create(ctx, newBoard)
	if err != nil {
		return err
	}

	objID, err := primitive.ObjectIDFromHex(boardID)
	if err != nil {
		return errors.New(errors.Service, "invalid board ID", errors.Internal, err)
	}

	// Add default categories
	categories := []model.Category{
		model.Professional,
		model.Financial,
		model.Leisure,
		model.Emotional,
		model.Intellectual,
		model.Spiritual,
		model.Physical,
		model.Intimate,
		model.Social,
		model.Familial,
	}

	return s.Repository.CreateCategories(ctx, objID, categories)
}

// FindPersonalDreams retrieves all personal (non-shared) dreams for a user
func (s *service) FindPersonalDreams(ctx context.Context, userID string) ([]*model.Dream, error) {
	dreamboard, err := s.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	var personalDreams []*model.Dream
	for _, dream := range dreamboard.Dreams {
		if !dream.IsShared {
			personalDreams = append(personalDreams, dream)
		}
	}

	return personalDreams, nil
}

// FindSharedDreams retrieves all shared dreams where the user is creator or active contributor
func (s *service) FindSharedDreams(ctx context.Context, userID string) ([]*model.Dream, error) {
	// Get user's contributions
	contributions, err := s.SharedDreamService.GetContributionsByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	var sharedDreams []*model.Dream
	dreamIDs := make(map[string]bool) // To avoid duplicates

	// Get dreams where user is an active contributor
	for _, contribution := range contributions {
		if contribution.Status == model.ContributionStatusActive {
			if !dreamIDs[contribution.DreamID] {
				// Find the dream in the user's dreamboard or other users' dreamboards
				dream, err := s.findDreamByID(ctx, contribution.DreamID)
				if err == nil && dream != nil {
					sharedDreams = append(sharedDreams, dream)
					dreamIDs[contribution.DreamID] = true
				}
			}
		}
	}

	return sharedDreams, nil
}

// findDreamByID is a helper method to find a dream by ID across all dreamboards
func (s *service) findDreamByID(ctx context.Context, dreamID string) (*model.Dream, error) {
	// This is a simplified implementation
	// In a real scenario, we might need a more efficient way to find dreams by ID
	// across all dreamboards, possibly with a dedicated index or query

	objID, err := primitive.ObjectIDFromHex(dreamID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dream ID", errors.BadRequest, err)
	}

	// For now, we'll need to search through dreamboards
	// This is not efficient and should be optimized in production
	dreamboards, err := s.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, dreamboard := range dreamboards {
		for _, dream := range dreamboard.Dreams {
			if dream.ObjectID == objID {
				return dream, nil
			}
		}
	}

	return nil, errors.New(errors.Service, "dream not found", errors.NotFound, nil)
}

// Helper
func (s *service) getColor(category string) string {
	switch category {
	case model.Professional.Identifier:
		return model.Professional.Color
	case model.Financial.Identifier:
		return model.Financial.Color
	case model.Leisure.Identifier:
		return model.Leisure.Color
	case model.Emotional.Identifier:
		return model.Emotional.Color
	case model.Intellectual.Identifier:
		return model.Intellectual.Color
	case model.Spiritual.Identifier:
		return model.Spiritual.Color
	case model.Physical.Identifier:
		return model.Physical.Color
	case model.Intimate.Identifier:
		return model.Intimate.Color
	case model.Social.Identifier:
		return model.Social.Color
	case model.Familial.Identifier:
		return model.Familial.Color
	default:
		return model.UndefinedCategory.Color
	}
}

// generateInviteURL creates a shareable invite URL using the APP_URL environment variable
func generateInviteURL(token string) string {
	appURL := os.Getenv("APP_URL")
	if appURL == "" {
		appURL = "http://localhost:8080" // fallback to development URL
	}
	return fmt.Sprintf("%s/dreamboards/dreams/join?code=%s", appURL, token)
}
