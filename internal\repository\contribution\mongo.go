package contribution

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.CONTRIBUTIONS_COLLECTION),
	}

	// Create compound index on dreamId and contributorUserId
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{
				{Key: "dreamId", Value: 1},
				{Key: "contributorUserId", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		log.Println("warning: failed to create compound index on contribution.dreamId and contributorUserId fields")
	}

	// Create index on dreamId field
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "dreamId", Value: 1}},
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on contribution.dreamId field")
	}

	// Create index on contributorUserId field
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "contributorUserId", Value: 1}},
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on contribution.contributorUserId field")
	}

	return repo
}

// Create creates a new contribution
func (m mongoDB) Create(ctx context.Context, contribution *model.Contribution) (string, error) {
	contribution.JoinedAt = time.Now()
	contribution.UpdatedAt = contribution.JoinedAt

	insertedResult, err := m.collection.InsertOne(ctx, contribution)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.New(errors.Repository, "user already contributing to this dream", errors.Conflict, err)
		}
		return "", errors.New(errors.Repository, "failed to create contribution", errors.Internal, err)
	}
	return insertedResult.InsertedID.(primitive.ObjectID).Hex(), nil
}

// Find finds a contribution by ID
func (m mongoDB) Find(ctx context.Context, id primitive.ObjectID) (*model.Contribution, error) {
	var contribution model.Contribution
	err := m.collection.FindOne(ctx, bson.D{{Key: "_id", Value: id}}).Decode(&contribution)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "contribution not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find contribution", errors.Internal, err)
	}

	contribution.ID = contribution.ObjectID.Hex()
	return &contribution, nil
}

// FindByDreamID finds all contributions for a dream
func (m mongoDB) FindByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error) {
	cursor, err := m.collection.Find(ctx, bson.D{{Key: "dreamId", Value: dreamID}})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find contributions by dream ID", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var contributions []*model.Contribution
	for cursor.Next(ctx) {
		var contribution model.Contribution
		if err = cursor.Decode(&contribution); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode contribution", errors.Internal, err)
		}
		contribution.ID = contribution.ObjectID.Hex()
		contributions = append(contributions, &contribution)
	}
	return contributions, nil
}

// FindByUserID finds all contributions for a user
func (m mongoDB) FindByUserID(ctx context.Context, userID string) ([]*model.Contribution, error) {
	cursor, err := m.collection.Find(ctx, bson.D{{Key: "contributorUserId", Value: userID}})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find contributions by user ID", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var contributions []*model.Contribution
	for cursor.Next(ctx) {
		var contribution model.Contribution
		if err = cursor.Decode(&contribution); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode contribution", errors.Internal, err)
		}
		contribution.ID = contribution.ObjectID.Hex()
		contributions = append(contributions, &contribution)
	}
	return contributions, nil
}

// FindByDreamAndUser finds a contribution by dream ID and user ID
func (m mongoDB) FindByDreamAndUser(ctx context.Context, dreamID, userID string) (*model.Contribution, error) {
	var contribution model.Contribution
	filter := bson.D{
		{Key: "dreamId", Value: dreamID},
		{Key: "contributorUserId", Value: userID},
	}
	err := m.collection.FindOne(ctx, filter).Decode(&contribution)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "contribution not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find contribution by dream and user", errors.Internal, err)
	}

	contribution.ID = contribution.ObjectID.Hex()
	return &contribution, nil
}

// FindActiveByDreamID finds all active contributions for a dream
func (m mongoDB) FindActiveByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error) {
	filter := bson.D{
		{Key: "dreamId", Value: dreamID},
		{Key: "status", Value: model.ContributionStatusActive},
	}
	cursor, err := m.collection.Find(ctx, filter)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find active contributions by dream ID", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var contributions []*model.Contribution
	for cursor.Next(ctx) {
		var contribution model.Contribution
		if err = cursor.Decode(&contribution); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode contribution", errors.Internal, err)
		}
		contribution.ID = contribution.ObjectID.Hex()
		contributions = append(contributions, &contribution)
	}
	return contributions, nil
}

// Update updates a contribution
func (m mongoDB) Update(ctx context.Context, contribution *model.Contribution) error {
	if contribution.ObjectID.IsZero() {
		return errors.New(errors.Repository, "invalid contribution ID", errors.BadRequest, nil)
	}

	contribution.UpdatedAt = time.Now()

	opts := options.Update().SetUpsert(false)
	result, err := m.collection.UpdateOne(ctx,
		bson.D{{Key: "_id", Value: contribution.ObjectID}},
		bson.D{{Key: "$set", Value: contribution}},
		opts,
	)
	if err != nil {
		return errors.New(errors.Repository, "failed to update contribution", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "contribution not found", errors.NotFound, nil)
	}
	return nil
}

// UpdateStatusByDreamID updates the status of all contributions for a dream
func (m mongoDB) UpdateStatusByDreamID(ctx context.Context, dreamID string, status model.ContributionStatus) error {
	filter := bson.D{{Key: "dreamId", Value: dreamID}}
	update := bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "status", Value: status},
			{Key: "updatedAt", Value: time.Now()},
		}},
	}

	_, err := m.collection.UpdateMany(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update contributions status by dream ID", errors.Internal, err)
	}
	return nil
}

// Delete deletes a contribution
func (m mongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.collection.DeleteOne(ctx, bson.D{{Key: "_id", Value: id}})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete contribution", errors.Internal, err)
	}
	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "contribution not found", errors.NotFound, nil)
	}
	return nil
}
