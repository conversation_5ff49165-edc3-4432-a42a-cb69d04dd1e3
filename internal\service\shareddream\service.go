package shareddream

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"os"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/dsoplabs/dinbora-backend/internal/repository/contribution"
	"github.com/dsoplabs/dinbora-backend/internal/repository/sharelink"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Service interface {
	// Share Link Management
	CreateShareLink(ctx context.Context, dreamID string) (*model.ShareLink, error)
	GetShareLink(ctx context.Context, dreamID string) (*model.ShareLink, error)
	GetShareLinkByToken(ctx context.Context, token string) (*model.ShareLink, error)
	UpdateShareLinkStatus(ctx context.Context, dreamID string, isEnabled bool) error
	RegenerateShareLink(ctx context.Context, dreamID string) (*model.ShareLink, error)

	// Invitation Management
	GetInviteDetails(ctx context.Context, token string) (*InviteDetailsDTO, error)
	JoinSharedDream(ctx context.Context, token string, userID string, isCreator bool, monthlyPledgedAmount monetary.Amount) (*model.Contribution, error)

	// Contribution Management
	GetContributionsByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error)
	GetActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error)
	GetContributionsByUserID(ctx context.Context, userID string) ([]*model.Contribution, error)
	UpdateContributionStatus(ctx context.Context, dreamID string, status model.ContributionStatus) error

	// Dream Management
	CalculateDreamDuration(ctx context.Context, dreamID string, estimatedCost monetary.Amount) (*int, error)
	GetDreamDashboard(ctx context.Context, dreamID string, userID string) (*DreamDashboardDTO, error)
}

type service struct {
	ShareLinkRepo    sharelink.Repository
	ContributionRepo contribution.Repository
}

func New(shareLinkRepo sharelink.Repository, contributionRepo contribution.Repository) Service {
	return &service{
		ShareLinkRepo:    shareLinkRepo,
		ContributionRepo: contributionRepo,
	}
}

// InviteDetailsDTO contains information about a shared dream invitation
type InviteDetailsDTO struct {
	CreatorName                    string          `json:"creatorName"`
	DreamTitle                     string          `json:"dreamTitle"`
	DreamTotalCost                 monetary.Amount `json:"dreamTotalCost"`
	CurrentEstimatedDurationMonths int             `json:"currentEstimatedDurationMonths"`
}

// DreamDashboardDTO contains comprehensive information for the shared dream dashboard
type DreamDashboardDTO struct {
	DreamDetails        DreamDetailsDTO  `json:"dreamDetails"`
	Contributors        []ContributorDTO `json:"contributors"`
	TransactionsHistory []TransactionDTO `json:"transactionsHistory"`
}

// DreamDetailsDTO contains basic dream information for the dashboard
type DreamDetailsDTO struct {
	ID                      string               `json:"id"`
	Title                   string               `json:"title"`
	IsCreator               bool                 `json:"isCreator"`
	TotalCost               monetary.Amount      `json:"totalCost"`
	RaisedAmount            monetary.Amount      `json:"raisedAmount"`
	RemainingAmount         monetary.Amount      `json:"remainingAmount"`
	EstimatedDurationMonths *int                 `json:"estimatedDurationMonths"`
	FundingStatus           string               `json:"fundingStatus"`
	ShareLinkDetails        *ShareLinkDetailsDTO `json:"shareLinkDetails,omitempty"`
}

// ShareLinkDetailsDTO contains share link information for creators
type ShareLinkDetailsDTO struct {
	URL       string    `json:"url"`
	IsEnabled bool      `json:"isEnabled"`
	ExpiresAt time.Time `json:"expiresAt"`
}

// ContributorDTO contains information about a dream contributor
type ContributorDTO struct {
	UserID                        string          `json:"userId"`
	UserName                      string          `json:"userName"`
	UserAvatarURL                 string          `json:"userAvatarUrl"`
	IsCreatorFlag                 bool            `json:"isCreatorFlag"`
	MonthlyPledgedAmount          monetary.Amount `json:"monthlyPledgedAmount"`
	CurrentMonthPaidAmount        monetary.Amount `json:"currentMonthPaidAmount"`
	PledgePaidPercentageThisMonth float64         `json:"pledgePaidPercentageThisMonth"`
}

// TransactionDTO contains information about a payment transaction
type TransactionDTO struct {
	ParticipantName string          `json:"participantName"`
	AmountPaid      monetary.Amount `json:"amountPaid"`
	PaymentDate     string          `json:"paymentDate"`
}

// CreateShareLink creates a new share link for a dream
func (s *service) CreateShareLink(ctx context.Context, dreamID string) (*model.ShareLink, error) {
	// Check if share link already exists
	existing, err := s.ShareLinkRepo.FindByDreamID(ctx, dreamID)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return nil, err
	}
	if existing != nil {
		return existing, nil
	}

	// Generate unique token
	token, err := generateSecureToken()
	if err != nil {
		return nil, errors.New(errors.Service, "failed to generate secure token", errors.Internal, err)
	}

	shareLink := &model.ShareLink{
		DreamID:   dreamID,
		Token:     token,
		IsEnabled: true,
		ExpiresAt: time.Now().Add(7 * 24 * time.Hour), // 7 days
	}

	if err := shareLink.Validate(); err != nil {
		return nil, err
	}

	shareLinkID, err := s.ShareLinkRepo.Create(ctx, shareLink)
	if err != nil {
		return nil, err
	}

	objID, err := primitive.ObjectIDFromHex(shareLinkID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid share link ID", errors.Internal, err)
	}

	return s.ShareLinkRepo.Find(ctx, objID)
}

// GetShareLink retrieves a share link by dream ID
func (s *service) GetShareLink(ctx context.Context, dreamID string) (*model.ShareLink, error) {
	return s.ShareLinkRepo.FindByDreamID(ctx, dreamID)
}

// GetShareLinkByToken retrieves a share link by token
func (s *service) GetShareLinkByToken(ctx context.Context, token string) (*model.ShareLink, error) {
	return s.ShareLinkRepo.FindByToken(ctx, token)
}

// UpdateShareLinkStatus updates the enabled status of a share link
func (s *service) UpdateShareLinkStatus(ctx context.Context, dreamID string, isEnabled bool) error {
	shareLink, err := s.ShareLinkRepo.FindByDreamID(ctx, dreamID)
	if err != nil {
		return err
	}

	shareLink.IsEnabled = isEnabled
	shareLink.UpdatedAt = time.Now()

	return s.ShareLinkRepo.Update(ctx, shareLink)
}

// RegenerateShareLink creates a new token for an existing share link
func (s *service) RegenerateShareLink(ctx context.Context, dreamID string) (*model.ShareLink, error) {
	shareLink, err := s.ShareLinkRepo.FindByDreamID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	// Generate new token
	newToken, err := generateSecureToken()
	if err != nil {
		return nil, errors.New(errors.Service, "failed to generate secure token", errors.Internal, err)
	}

	shareLink.Token = newToken
	shareLink.ExpiresAt = time.Now().Add(7 * 24 * time.Hour) // Reset expiration
	shareLink.UpdatedAt = time.Now()

	if err := s.ShareLinkRepo.Update(ctx, shareLink); err != nil {
		return nil, err
	}

	return shareLink, nil
}

// GetContributionsByDreamID retrieves all contributions for a dream
func (s *service) GetContributionsByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error) {
	return s.ContributionRepo.FindByDreamID(ctx, dreamID)
}

// GetActiveContributionsByDreamID retrieves all active contributions for a dream
func (s *service) GetActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error) {
	return s.ContributionRepo.FindActiveByDreamID(ctx, dreamID)
}

// GetContributionsByUserID retrieves all contributions for a user
func (s *service) GetContributionsByUserID(ctx context.Context, userID string) ([]*model.Contribution, error) {
	return s.ContributionRepo.FindByUserID(ctx, userID)
}

// UpdateContributionStatus updates the status of all contributions for a dream
func (s *service) UpdateContributionStatus(ctx context.Context, dreamID string, status model.ContributionStatus) error {
	return s.ContributionRepo.UpdateStatusByDreamID(ctx, dreamID, status)
}

// CalculateDreamDuration calculates the estimated duration in months for a dream
func (s *service) CalculateDreamDuration(ctx context.Context, dreamID string, estimatedCost monetary.Amount) (*int, error) {
	activeContributions, err := s.GetActiveContributionsByDreamID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	var totalMonthlyPledged monetary.Amount
	for _, contribution := range activeContributions {
		totalMonthlyPledged += contribution.MonthlyPledgedAmount
	}

	if totalMonthlyPledged == 0 {
		return nil, nil // Cannot calculate duration with zero contributions
	}

	duration := int(estimatedCost / totalMonthlyPledged)
	return &duration, nil
}

// generateSecureToken generates a cryptographically secure random token
func generateSecureToken() (string, error) {
	bytes := make([]byte, 16) // 128 bits
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// GetInviteDetails retrieves details about a shared dream invitation
func (s *service) GetInviteDetails(ctx context.Context, token string) (*InviteDetailsDTO, error) {
	shareLink, err := s.GetShareLinkByToken(ctx, token)
	if err != nil {
		return nil, err
	}

	// Validate share link
	if !shareLink.IsEnabled {
		return nil, errors.New(errors.Service, "invite link is disabled", errors.BadRequest, nil)
	}
	if shareLink.ExpiresAt.Before(time.Now()) {
		return nil, errors.New(errors.Service, "invite link has expired", errors.BadRequest, nil)
	}

	// Get active contributions to calculate current duration
	activeContributions, err := s.GetActiveContributionsByDreamID(ctx, shareLink.DreamID)
	if err != nil {
		return nil, err
	}

	var totalMonthlyPledged monetary.Amount
	for _, contribution := range activeContributions {
		totalMonthlyPledged += contribution.MonthlyPledgedAmount
	}

	// Note: We need dream details and creator info from the dreamboard service
	// This is a placeholder - in real implementation, we'd need to inject the dreamboard service
	// or get this information from the caller
	return &InviteDetailsDTO{
		CreatorName:                    "Creator Name", // TODO: Get from user service
		DreamTitle:                     "Dream Title",  // TODO: Get from dream
		DreamTotalCost:                 0,              // TODO: Get from dream
		CurrentEstimatedDurationMonths: 0,              // TODO: Calculate based on totalMonthlyPledged
	}, nil
}

// JoinSharedDream allows a user to join a shared dream
func (s *service) JoinSharedDream(ctx context.Context, token string, userID string, isCreator bool, monthlyPledgedAmount monetary.Amount) (*model.Contribution, error) {
	shareLink, err := s.GetShareLinkByToken(ctx, token)
	if err != nil {
		return nil, err
	}

	// Validate share link
	if !shareLink.IsEnabled {
		return nil, errors.New(errors.Service, "invite link is disabled", errors.BadRequest, nil)
	}
	if shareLink.ExpiresAt.Before(time.Now()) {
		return nil, errors.New(errors.Service, "invite link has expired", errors.BadRequest, nil)
	}

	// Check if user already has an active contribution for this dream
	existing, err := s.ContributionRepo.FindByDreamAndUser(ctx, shareLink.DreamID, userID)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return nil, err
	}
	if existing != nil && existing.Status == model.ContributionStatusActive {
		return nil, errors.New(errors.Service, "user already contributing to this dream", errors.Conflict, nil)
	}

	// Create new contribution
	contribution := &model.Contribution{
		DreamID:              shareLink.DreamID,
		ContributorUserID:    userID,
		IsCreator:            isCreator,
		MonthlyPledgedAmount: monthlyPledgedAmount,
		Status:               model.ContributionStatusActive,
	}

	if err := contribution.Validate(); err != nil {
		return nil, err
	}

	contributionID, err := s.ContributionRepo.Create(ctx, contribution)
	if err != nil {
		return nil, err
	}

	objID, err := primitive.ObjectIDFromHex(contributionID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid contribution ID", errors.Internal, err)
	}

	return s.ContributionRepo.Find(ctx, objID)
}

// GetDreamDashboard retrieves comprehensive dashboard information for a shared dream
func (s *service) GetDreamDashboard(ctx context.Context, dreamID string, userID string) (*DreamDashboardDTO, error) {
	// Get active contributions
	activeContributions, err := s.GetActiveContributionsByDreamID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	// Check if user has access (is creator or active contributor)
	hasAccess := false
	isCreator := false
	for _, contribution := range activeContributions {
		if contribution.ContributorUserID == userID && contribution.Status == model.ContributionStatusActive {
			hasAccess = true
			if contribution.IsCreator {
				isCreator = true
			}
			break
		}
	}

	if !hasAccess {
		return nil, errors.New(errors.Service, "user does not have access to this dream dashboard", errors.Forbidden, nil)
	}

	// Build contributors list
	contributors := make([]ContributorDTO, 0, len(activeContributions))
	for _, contribution := range activeContributions {
		// TODO: Get user details from user service
		// TODO: Get current month paid amount from financial sheet service
		contributor := ContributorDTO{
			UserID:                        contribution.ContributorUserID,
			UserName:                      "User Name", // TODO: Get from user service
			UserAvatarURL:                 "",          // TODO: Get from user service
			IsCreatorFlag:                 contribution.IsCreator,
			MonthlyPledgedAmount:          contribution.MonthlyPledgedAmount,
			CurrentMonthPaidAmount:        0, // TODO: Get from financial sheet service
			PledgePaidPercentageThisMonth: 0, // TODO: Calculate
		}
		contributors = append(contributors, contributor)
	}

	// Get share link details if user is creator
	var shareLinkDetails *ShareLinkDetailsDTO
	if isCreator {
		shareLink, err := s.GetShareLink(ctx, dreamID)
		if err == nil {
			shareLinkDetails = &ShareLinkDetailsDTO{
				URL:       generateInviteURL(shareLink.Token),
				IsEnabled: shareLink.IsEnabled,
				ExpiresAt: shareLink.ExpiresAt,
			}
		}
	}

	// TODO: Get dream details from dreamboard service
	// TODO: Get transaction history from financial sheet service
	dreamDetails := DreamDetailsDTO{
		ID:                      dreamID,
		Title:                   "Dream Title", // TODO: Get from dream
		IsCreator:               isCreator,
		TotalCost:               0,   // TODO: Get from dream
		RaisedAmount:            0,   // TODO: Get from financial sheet service
		RemainingAmount:         0,   // TODO: Calculate
		EstimatedDurationMonths: nil, // TODO: Calculate
		FundingStatus:           "",  // TODO: Get from dream
		ShareLinkDetails:        shareLinkDetails,
	}

	return &DreamDashboardDTO{
		DreamDetails:        dreamDetails,
		Contributors:        contributors,
		TransactionsHistory: []TransactionDTO{}, // TODO: Get from financial sheet service
	}, nil
}

// generateInviteURL creates a shareable invite URL using the APP_URL environment variable
func generateInviteURL(token string) string {
	appURL := os.Getenv("APP_URL")
	if appURL == "" {
		appURL = "http://localhost:8080" // fallback to development URL
	}
	return fmt.Sprintf("%s/dreamboards/dreams/join?code=%s", appURL, token)
}
