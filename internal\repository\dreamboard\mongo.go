package dreamboard

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
	trash      *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.DREAMBOARDS_COLLECTION),
		trash:      db.Collection(repository.DREAMBOARDS_COLLECTION_TRASH),
	}

	// Create unique index on user field
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "user", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on dreamboard.user field")
		db.Client().Disconnect(context.Background())
	}

	// Create index on categories._id field
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "categories._id", Value: 1}},
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on dreamboard.categories._id field")
		db.Client().Disconnect(context.Background())
	}

	// Create index on categories.identifier field
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "categories.identifier", Value: 1}},
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on dreamboard.categories.identifier field")
		db.Client().Disconnect(context.Background())
	}

	// Create index on dreams._id field
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "dreams._id", Value: 1}},
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on dreamboard.dreams._id field")
		db.Client().Disconnect(context.Background())
	}

	return repo
}

// CRUD
func (m mongoDB) Create(ctx context.Context, dreamboard *model.Dreamboard) (string, error) {
	insertedResult, err := m.collection.InsertOne(ctx, dreamboard)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.New(errors.Repository, errors.DreamboardConflictExists, errors.Conflict, err)
		}
		return "", errors.New(errors.Repository, errors.DreamboardCreateFailed, errors.Internal, err)
	}
	return insertedResult.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (m mongoDB) CreateDelete(ctx context.Context, deletedBoard *model.DeletedDreamboard) error {
	_, err := m.trash.InsertOne(ctx, deletedBoard)
	if err != nil {
		return errors.New(errors.Repository, errors.DreamboardDeleteCreateFailed, errors.Internal, err)
	}
	return nil
}

func (m mongoDB) Find(ctx context.Context, id primitive.ObjectID) (*model.Dreamboard, error) {
	var dreamboard model.Dreamboard
	err := m.collection.FindOne(ctx, bson.D{{Key: "_id", Value: id}}).Decode(&dreamboard)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.DreamboardNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.DreamboardFindFailed, errors.Internal, err)
	}

	// Convert ObjectID to hex string for external use
	if dreamboard.ObjectID.IsZero() {
		return nil, errors.New(errors.Repository, errors.DreamboardInvalidID, errors.BadRequest, nil)
	}

	dreamboard.ID = dreamboard.ObjectID.Hex()
	return &dreamboard, nil
}

func (m mongoDB) FindAll(ctx context.Context) ([]*model.Dreamboard, error) {
	cursor, err := m.collection.Find(ctx, bson.D{})
	if err != nil {
		return nil, errors.New(errors.Repository, errors.DreamboardFindAllFailed, errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var boards []*model.Dreamboard
	for cursor.Next(ctx) {
		var dreamboard model.Dreamboard
		if err = cursor.Decode(&dreamboard); err != nil {
			return nil, errors.New(errors.Repository, errors.DreamboardDecodeFailed, errors.Internal, err)
		}
		dreamboard.ID = dreamboard.ObjectID.Hex()
		boards = append(boards, &dreamboard)
	}
	return boards, nil
}

func (m mongoDB) FindByUser(ctx context.Context, userID string) (*model.Dreamboard, error) {
	var dreamboard model.Dreamboard
	err := m.collection.FindOne(ctx, bson.D{{Key: "user", Value: userID}}).Decode(&dreamboard)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.DreamboardNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.DreamboardFindByUserFailed, errors.Internal, err)
	}
	dreamboard.ID = dreamboard.ObjectID.Hex()
	return &dreamboard, nil
}

func (m mongoDB) FindDeletedByUser(ctx context.Context, userID string) (*model.DeletedDreamboard, error) {
	var deletedBoard model.DeletedDreamboard
	err := m.trash.FindOne(ctx, bson.D{{Key: "dreamboard.user", Value: userID}}).Decode(&deletedBoard)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.DeletedDreamboardNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.DeletedDreamboardFindFailed, errors.Internal, err)
	}
	deletedBoard.Dreamboard.ID = deletedBoard.Dreamboard.ObjectID.Hex()
	return &deletedBoard, nil
}

func (m mongoDB) Update(ctx context.Context, dreamboard *model.Dreamboard) error {
	if dreamboard.ObjectID.IsZero() {
		return errors.New(errors.Repository, errors.DreamboardInvalidID, errors.BadRequest, nil)
	}

	dreamboard.UpdatedAt = time.Now()

	opts := options.Update().SetUpsert(false)
	result, err := m.collection.UpdateOne(ctx,
		bson.D{{Key: "_id", Value: dreamboard.ObjectID}},
		bson.D{{Key: "$set", Value: dreamboard}},
		opts,
	)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, errors.DreamboardConflictUpdate, errors.Conflict, err)
		}
		return errors.New(errors.Repository, errors.DreamboardUpdateFailed, errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, errors.DreamboardNotFound, errors.NotFound, nil)
	}
	return nil
}

func (m mongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.collection.DeleteOne(ctx, bson.D{{Key: "_id", Value: id}})
	if err != nil {
		return errors.New(errors.Repository, errors.DreamboardDeleteFailed, errors.Internal, err)
	}
	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, errors.DreamboardNotFound, errors.NotFound, nil)
	}
	return nil
}

// Category Management
func (m mongoDB) CreateCategory(ctx context.Context, boardID primitive.ObjectID, category *model.Category) error {
	category.ObjectID = primitive.NewObjectID()

	update := bson.D{
		{Key: "$push", Value: bson.D{{Key: "categories", Value: category}}},
		{Key: "$set", Value: bson.D{{Key: "updatedAt", Value: time.Now()}}},
	}

	result, err := m.collection.UpdateByID(ctx, boardID, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to add category", errors.Internal, err)
	}
	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "dreamboard not found", errors.NotFound, nil)
	}
	return nil
}

func (m mongoDB) CreateCategories(ctx context.Context, boardID primitive.ObjectID, categories []model.Category) error {
	session, err := m.collection.Database().Client().StartSession()
	if err != nil {
		return errors.New(errors.Repository, "failed to start session", errors.Internal, err)
	}
	defer session.EndSession(ctx)

	callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
		for i := range categories {
			categories[i].ObjectID = primitive.NewObjectID()
			update := bson.D{
				{Key: "$push", Value: bson.D{{Key: "categories", Value: categories[i]}}},
				{Key: "$set", Value: bson.D{{Key: "updatedAt", Value: time.Now()}}},
			}

			result, err := m.collection.UpdateByID(sessCtx, boardID, update)
			if err != nil {
				return nil, errors.New(errors.Repository, "failed to add category", errors.Internal, err)
			}
			if result.MatchedCount == 0 {
				return nil, errors.New(errors.Repository, "dreamboard not found", errors.NotFound, nil)
			}
		}
		return nil, nil
	}

	_, err = session.WithTransaction(ctx, callback)
	if err != nil {
		return errors.New(errors.Repository, "failed to create categories in transaction", errors.Internal, err)
	}

	return nil
}

func (m mongoDB) FindCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) (*model.Category, error) {
	var dreamboard model.Dreamboard
	filter := bson.D{
		{Key: "_id", Value: boardID},
		{Key: "categories._id", Value: categoryID},
	}
	opts := options.FindOne().SetProjection(bson.D{
		{Key: "categories.$", Value: 1},
	})

	err := m.collection.FindOne(ctx, filter, opts).Decode(&dreamboard)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "category not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find category", errors.Internal, err)
	}

	if len(dreamboard.Categories) > 0 {
		return dreamboard.Categories[0], nil
	}

	return nil, errors.New(errors.Repository, "category not found", errors.NotFound, nil)
}

func (m mongoDB) UpdateCategory(ctx context.Context, boardID primitive.ObjectID, category *model.Category) error {
	update := bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "categories.$[elem]", Value: category},
			{Key: "updatedAt", Value: time.Now()},
		}},
	}

	opts := options.Update().SetArrayFilters(options.ArrayFilters{
		Filters: []interface{}{bson.M{"elem._id": category.ObjectID}},
	})

	result, err := m.collection.UpdateByID(ctx, boardID, update, opts)
	if err != nil {
		return errors.New(errors.Repository, "failed to update category", errors.Internal, err)
	}
	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "category not found", errors.NotFound, nil)
	}
	return nil
}

func (m mongoDB) DeleteCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) error {
	update := bson.D{
		{Key: "$pull", Value: bson.D{{Key: "categories", Value: bson.D{{Key: "_id", Value: categoryID}}}}},
		{Key: "$set", Value: bson.D{{Key: "updatedAt", Value: time.Now()}}},
	}

	result, err := m.collection.UpdateByID(ctx, boardID, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to delete category", errors.Internal, err)
	}
	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "category not found", errors.NotFound, nil)
	}
	return nil
}

// Dream Management
func (m mongoDB) CreateDream(ctx context.Context, boardID primitive.ObjectID, dream *model.Dream) error {
	dream.ObjectID = primitive.NewObjectID()

	update := bson.D{
		{Key: "$push", Value: bson.D{{Key: "dreams", Value: dream}}},
		{Key: "$set", Value: bson.D{{Key: "updatedAt", Value: time.Now()}}},
	}

	_, err := m.collection.UpdateByID(ctx, boardID, update)
	if err != nil {
		return errors.New(errors.Repository, errors.DreamAddFailed, errors.Internal, err)
	}

	// Convert ObjectID to hex string for external use
	dream.ID = dream.ObjectID.Hex()
	return nil
}

func (m mongoDB) FindDream(ctx context.Context, boardID, dreamID primitive.ObjectID) (*model.Dream, error) {
	var dreamboard model.Dreamboard
	filter := bson.D{
		{Key: "_id", Value: boardID},
		{Key: "dreams._id", Value: dreamID},
	}
	// Use projection to only return the matching dream
	opts := options.FindOne().SetProjection(bson.D{
		{Key: "dreams.$", Value: 1},
	})

	err := m.collection.FindOne(ctx, filter, opts).Decode(&dreamboard)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.DreamNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.DreamboardFindFailed, errors.Internal, err)
	}

	if len(dreamboard.Dreams) > 0 {
		return dreamboard.Dreams[0], nil
	}

	return nil, errors.New(errors.Repository, errors.DreamNotFound, errors.NotFound, nil)
}

func (m mongoDB) UpdateDream(ctx context.Context, boardID primitive.ObjectID, dream *model.Dream) error {
	dream.UpdatedAt = time.Now()

	update := bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "dreams.$[elem]", Value: dream},
			{Key: "updatedAt", Value: time.Now()},
		}},
	}

	opts := options.Update().SetArrayFilters(options.ArrayFilters{
		Filters: []interface{}{bson.M{"elem._id": dream.ObjectID}},
	})

	result, err := m.collection.UpdateByID(ctx, boardID, update, opts)
	if err != nil {
		return errors.New(errors.Repository, errors.DreamUpdateFailed, errors.Internal, err)
	}
	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, errors.DreamNotFound, errors.NotFound, nil)
	}
	return nil
}

func (m mongoDB) RemoveDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) error {
	update := bson.D{
		{Key: "$pull", Value: bson.D{{Key: "dreams", Value: bson.D{{Key: "_id", Value: dreamID}}}}},
		{Key: "$set", Value: bson.D{{Key: "updatedAt", Value: time.Now()}}},
	}

	result, err := m.collection.UpdateByID(ctx, boardID, update)
	if err != nil {
		return errors.New(errors.Repository, errors.DreamRemoveFailed, errors.Internal, err)
	}
	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, errors.DreamboardNotFound, errors.NotFound, nil)
	}
	return nil
}
