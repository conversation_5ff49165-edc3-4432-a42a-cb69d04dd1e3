package sharelink

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Reader interface {
	// Read operations
	Find(ctx context.Context, id primitive.ObjectID) (*model.ShareLink, error)
	FindByToken(ctx context.Context, token string) (*model.ShareLink, error)
	FindByDreamID(ctx context.Context, dreamID string) (*model.ShareLink, error)
}

type Writer interface {
	// Create operations
	Create(ctx context.Context, shareLink *model.ShareLink) (string, error)

	// Update operations
	Update(ctx context.Context, shareLink *model.ShareLink) error

	// Delete operations
	Delete(ctx context.Context, id primitive.ObjectID) error
}

type Repository interface {
	Reader
	Writer
}
