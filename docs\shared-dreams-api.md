# Shared Dreams API Documentation

## Overview

The Shared Dreams feature extends the existing Dreams functionality to allow users to create shared dreams where multiple participants can contribute monthly amounts towards achieving a common goal.

## Key Features

- **Personal Dreams**: Traditional individual dreams (existing functionality)
- **Shared Dreams**: Dreams that can be shared via invite links
- **Invite System**: Secure token-based invitation system
- **Contribution Tracking**: Track monthly pledged amounts from all participants
- **Dynamic Duration Calculation**: Automatically recalculate dream completion time based on total contributions
- **Dashboard**: Comprehensive view of shared dream progress

## API Endpoints

### 1. Create Dream (Enhanced)

**Endpoint**: `POST /api/v2/dreamboards/dreams`

**Request Body**:
```json
{
  "category": "TRAVEL",
  "title": "Trip to Europe",
  "timeFrame": "MEDIUM",
  "deadline": "2024-12-31T00:00:00Z",
  "estimatedCost": 500000,
  "monthlySavings": 50000,
  "moneySource": ["SALARY"],
  "isShared": true
}
```

**Response** (for shared dreams):
```json
{
  "dreams": [...],
  "shareUrl": "https://dinbora.com.br/dreamboards/dreams/join?code=ABCDE12345"
}
```

### 2. List Personal Dreams

**Endpoint**: `GET /api/v2/dreamboards/dreams/personal`

**Response**:
```json
[
  {
    "id": "dream-id",
    "title": "Personal Vacation",
    "isShared": false,
    "estimatedCost": 300000,
    "monthlySavings": 30000,
    "fundingStatus": "PERSONAL_ACTIVE"
  }
]
```

### 3. List Shared Dreams

**Endpoint**: `GET /api/v2/dreamboards/dreams/shared`

**Response**:
```json
[
  {
    "id": "dream-id",
    "title": "Group Trip to Europe",
    "isShared": true,
    "estimatedCost": 500000,
    "currentRaisedAmount": 120000,
    "calculatedDurationMonths": 8,
    "fundingStatus": "SHARED_ACTIVE_COLLECTING"
  }
]
```

### 4. Get Invite Details (Public)

**Endpoint**: `GET /api/v2/invite-details?code=ABCDE12345`

**Response**:
```json
{
  "creatorName": "John Doe",
  "dreamTitle": "Trip to Europe",
  "dreamTotalCost": 500000,
  "currentEstimatedDurationMonths": 10
}
```

### 5. Join Shared Dream

**Endpoint**: `POST /api/v2/invite/join?code=ABCDE12345`

**Request Body**:
```json
{
  "monthlyPledgedAmount": 25000
}
```

**Response**:
```json
{
  "id": "contribution-id",
  "dreamId": "dream-id",
  "contributorUserId": "user-id",
  "isCreator": false,
  "monthlyPledgedAmount": 25000,
  "status": "ACTIVE",
  "joinedAt": "2024-01-15T10:30:00Z"
}
```

### 6. Get Dream Dashboard

**Endpoint**: `GET /api/v2/dreams/{dreamId}/dashboard`

**Response**:
```json
{
  "dreamDetails": {
    "id": "dream-id",
    "title": "Trip to Europe",
    "isCreator": true,
    "totalCost": 500000,
    "raisedAmount": 120000,
    "remainingAmount": 380000,
    "estimatedDurationMonths": 8,
    "fundingStatus": "SHARED_ACTIVE_COLLECTING",
    "shareLinkDetails": {
      "url": "https://dinbora.com.br/dreamboards/dreams/join?code=ABCDE12345",
      "isEnabled": true,
      "expiresAt": "2024-01-22T10:30:00Z"
    }
  },
  "contributors": [
    {
      "userId": "user1",
      "userName": "John Doe",
      "userAvatarUrl": "https://...",
      "isCreatorFlag": true,
      "monthlyPledgedAmount": 50000,
      "currentMonthPaidAmount": 50000,
      "pledgePaidPercentageThisMonth": 100.0
    },
    {
      "userId": "user2",
      "userName": "Jane Smith",
      "userAvatarUrl": "https://...",
      "isCreatorFlag": false,
      "monthlyPledgedAmount": 25000,
      "currentMonthPaidAmount": 20000,
      "pledgePaidPercentageThisMonth": 80.0
    }
  ],
  "transactionsHistory": [
    {
      "participantName": "John Doe",
      "amountPaid": 50000,
      "paymentDate": "2024-01-15"
    }
  ]
}
```

## Funding Status Values

- `PERSONAL_ACTIVE`: Personal dream that is active
- `SHARED_OPEN_FOR_PARTICIPANTS`: Shared dream accepting new participants
- `SHARED_ACTIVE_COLLECTING`: Shared dream with participants, collecting contributions
- `FULLY_FUNDED`: Dream has reached its funding goal
- `COMPLETED`: Dream has been marked as completed
- `CANCELLED`: Dream has been cancelled

## Contribution Status Values

- `ACTIVE`: User is actively contributing to the dream
- `CANCELLED_BY_USER`: User cancelled their participation
- `ENDED_DREAM_CANCELLED`: Dream was cancelled by creator

## Authentication

All endpoints except `/invite-details` require authentication via Bearer token in the Authorization header.

## Error Handling

The API returns standard HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `409`: Conflict (e.g., user already contributing to dream)
- `500`: Internal Server Error

## Environment Variables

- `APP_URL`: Base URL for generating invite links (defaults to `http://localhost:8080`)

## Integration with Financial Sheet System

The system integrates with the existing Financial Sheet system to:
- Track actual payments made towards shared dreams
- Calculate current raised amounts
- Provide payment history for dashboard display
- Monitor monthly contribution fulfillment

## Security Considerations

- Share link tokens are cryptographically secure (128-bit random)
- Tokens expire after 7 days by default
- Access control ensures only creators and active contributors can view dream details
- Invite links can be disabled by creators to prevent new participants

## Data Types

### Monetary Values

All monetary values in the API are represented as integers (cents). For example:
- `500000` represents R$ 5000.00
- `25000` represents R$ 250.00
- `1` represents R$ 0.01

This ensures precision in financial calculations and avoids floating-point arithmetic issues.

## Available Endpoints

The following endpoints are ready for use:

1. **POST /api/v2/dreamboards/dreams** (with `isShared: true`)
2. **GET /api/v2/dreamboards/dreams/personal**
3. **GET /api/v2/dreamboards/dreams/shared**
4. **GET /api/v2/invite-details?code=TOKEN**
5. **POST /api/v2/invite/join?code=TOKEN**
6. **GET /api/v2/dreams/:dreamId/dashboard**
