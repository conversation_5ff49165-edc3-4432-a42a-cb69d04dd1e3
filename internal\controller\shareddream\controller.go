package shareddream

import (
	"context"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/dsoplabs/dinbora-backend/internal/service/shareddream"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// Invite Management
	GetInviteDetails() echo.HandlerFunc
	JoinSharedDream() echo.HandlerFunc

	// Dream Dashboard
	GetDreamDashboard() echo.HandlerFunc
}

type controller struct {
	Service shareddream.Service
}

func New(service shareddream.Service) Controller {
	return &controller{
		Service: service,
	}
}

// RegisterRoutes registers the shared dream routes
func (c *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	// Public routes (no auth required for invite details)
	currentGroup.GET("/invite-details", c.GetInviteDetails())

	// Protected routes
	protectedGroup := currentGroup.Group("", middlewares.AuthGuard())
	protectedGroup.POST("/invite/join", c.JoinSharedDream())
	protectedGroup.GET("/dreams/:dreamId/dashboard", c.GetDreamDashboard())
}

// GetInviteDetails retrieves details about a shared dream invitation
func (c *controller) GetInviteDetails() echo.HandlerFunc {
	return func(ctx echo.Context) error {
		code := ctx.QueryParam("code")
		if code == "" {
			return errors.New(errors.Controller, "code parameter is required", errors.BadRequest, nil)
		}

		inviteDetails, err := c.Service.GetInviteDetails(ctx.Request().Context(), code)
		if err != nil {
			return err
		}

		return ctx.JSON(http.StatusOK, inviteDetails)
	}
}

// JoinSharedDream allows a user to join a shared dream
func (c *controller) JoinSharedDream() echo.HandlerFunc {
	return func(ctx echo.Context) error {
		code := ctx.QueryParam("code")
		if code == "" {
			return errors.New(errors.Controller, "code parameter is required", errors.BadRequest, nil)
		}

		userToken, err := token.GetClaimsFromRequest(ctx.Request())
		if err != nil {
			return err
		}

		var requestBody struct {
			MonthlyPledgedAmount monetary.Amount `json:"monthlyPledgedAmount"`
		}
		if err := ctx.Bind(&requestBody); err != nil {
			return errors.New(errors.Controller, "invalid request body", errors.BadRequest, err)
		}

		contribution, err := c.Service.JoinSharedDream(
			ctx.Request().Context(),
			code,
			userToken.Uid,
			false, // Not the creator
			requestBody.MonthlyPledgedAmount,
		)
		if err != nil {
			return err
		}

		return ctx.JSON(http.StatusCreated, contribution)
	}
}

// GetDreamDashboard retrieves comprehensive dashboard information for a shared dream
func (c *controller) GetDreamDashboard() echo.HandlerFunc {
	return func(ctx echo.Context) error {
		dreamID := ctx.Param("dreamId")
		if dreamID == "" {
			return errors.New(errors.Controller, "dreamId parameter is required", errors.BadRequest, nil)
		}

		userToken, err := token.GetClaimsFromRequest(ctx.Request())
		if err != nil {
			return err
		}

		dashboard, err := c.Service.GetDreamDashboard(
			ctx.Request().Context(),
			dreamID,
			userToken.Uid,
		)
		if err != nil {
			return err
		}

		return ctx.JSON(http.StatusOK, dashboard)
	}
}
